name: Setup
description: Setup Env

runs:
  using: composite
  steps:
    - name: Upload Artifact arm64-v8a
      if: env.PACKAGE_TYPE != 'Android_5'
      uses: actions/upload-artifact@v4
      with:
        name: app-v${{ env.PACKAGE_VERSION }}-arm64-v8a-release
        path: android/app/build/outputs/apk/release/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-arm64-v8a.apk

    - name: Upload Artifact armeabi-v7a
      if: env.PACKAGE_TYPE == null
      uses: actions/upload-artifact@v4
      with:
        name: app-v${{ env.PACKAGE_VERSION }}-armeabi-v7a-release
        path: android/app/build/outputs/apk/release/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-armeabi-v7a.apk

    - name: Upload Artifact universal
      if: env.PACKAGE_TYPE != 'Android_SL'
      uses: actions/upload-artifact@v4
      with:
        name: app-v${{ env.PACKAGE_VERSION }}-universal-release
        path: android/app/build/outputs/apk/release/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-universal.apk

    - name: Upload Artifact x86_64
      if: env.PACKAGE_TYPE == null
      uses: actions/upload-artifact@v4
      with:
        name: app-v${{ env.PACKAGE_VERSION }}-x86_64-release
        path: android/app/build/outputs/apk/release/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-x86_64.apk

    - name: Upload Artifact x86
      if: env.PACKAGE_TYPE == null
      uses: actions/upload-artifact@v4
      with:
        name: app-v${{ env.PACKAGE_VERSION }}-x86-release
        path: android/app/build/outputs/apk/release/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-x86.apk
