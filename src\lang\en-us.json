{"add_to": "Add to...", "agree": "Agree", "agree_go": "Go to enable", "agree_to": "Go to settings", "back": "Back", "back_home": "Back to desktop", "cancel": "Cancel", "cancel_button_text_2": "No, I clicked wrong", "change_position": "Adjust position", "change_position_list_title": "Adjust list position", "change_position_music_multi_title": "Adjust the position of the selected {num} songs to", "change_position_music_title": "Adjust the position of \"{name}\" to", "change_position_tip": "Please enter a new position", "close": "Close", "collect": "Collect", "collect_songlist": "Collect songlist", "collect_success": "Collected successfully", "collect_toplist": "Collect toplist", "comment_hide_text": "<PERSON><PERSON> comments", "comment_not support": "This song does not support getting comments", "comment_refresh": "This is already the comment of \"{name}\"", "comment_show_image": "Show image", "comment_show_text": "Expand comments", "comment_tab_hot": "Hot {total}", "comment_tab_new": "New {total}", "comment_title": "Comments on \"{name}\"", "confirm": "Confirm", "confirm_button_text": "Yes", "confirm_tip": "Are you sure you want to do this?", "copy_name": "Share song", "copy_name_tip": "<PERSON>pied", "create_new_folder": "New folder", "create_new_folder_error_tip": "The entered name is invalid", "create_new_folder_tip": "Please enter a new folder name", "date_format_hour": "{num} hours ago", "date_format_minute": "{num} minutes ago", "date_format_second": "{num} seconds ago", "deep_link__handle_error_tip": "Call failed: {message}", "deep_link_file_js_confirm_tip": "Are you sure you want to import the custom source file \"{name}\"?", "deep_link_file_lxmc_confirm_tip": "Are you sure you want to import the list file \"{name}\"?", "delete": "Remove", "dialog_cancel": "No", "dialog_confirm": "OK", "disagree": "Disagree", "disagree_tip": "Never mind...🙄", "dislike": "Dislike", "duplicate_list_tip": "You have already collected the list \"{name}\", do you need to update the songs in it?", "edit_metadata": "Edit tags", "exit_app_tip": "Are you sure you want to exit the app?", "ignoring_battery_optimization_check_tip": "IKUN Music is not in the whitelist of \"Ignoring battery optimization\", which may cause the system to pause when playing music in the background. Do you want to add IKUN Music to the whitelist?", "ignoring_battery_optimization_check_title": "Background running permission setting reminder", "input_error": "Don't mess around😡", "list_add_btn_title": "Add this song to \"{name}\"", "list_add_tip_exists": "This song already exists in the list, don't click me again~😡", "list_add_title_first_add": "Add", "list_add_title_first_move": "Move", "list_add_title_last": "to...", "list_create": "New list", "list_create_input_placeholder": "What name do you want to give it...", "list_duplicate_tip": "A list with the same name already exists, do you want to continue creating it?", "list_edit_action_tip_add_failed": "Add failed", "list_edit_action_tip_add_success": "Added successfully", "list_edit_action_tip_exist": "This song already exists in this list", "list_edit_action_tip_move_failed": "Move failed", "list_edit_action_tip_move_success": "Moved successfully", "list_edit_action_tip_remove_success": "Removed successfully", "list_end": "That's all~", "list_error": "Load failed😥, click to try again", "list_export": "Export", "list_export_part_desc": "Select the save location of the list file", "list_import": "Import", "list_import_part_button_cancel": "No", "list_import_part_button_confirm": "Overwrite", "list_import_part_confirm": "The imported list \"{importName}\" has the same ID as the local list \"{localName}\", do you want to overwrite the local list?", "list_import_part_desc": "Select list file", "list_import_tip__alldata": "This is an \"all data\" backup file, you need to import it here:\n\nSettings → Backup and Restore → List Data → Import List", "list_import_tip__failed": "Import failed", "list_import_tip__playlist": "This is a \"list\" backup file, you need to import it here:\n\nSettings → Backup and Restore → List Data → Import List", "list_import_tip__playlist_part": "This is a \"single list\" file, you need to import it here:\n\nMy List → Click the menu button on the right of any list name → Select \"Import\" in the pop-up menu", "list_import_tip__setting": "This is a \"settings\" backup file, the mobile version does not support importing this type of file", "list_import_tip__unknown": "Unknown type, please try to upgrade to the latest version and try again", "list_loading": "Loading...", "list_multi_add_title_first_add": "Add selected", "list_multi_add_title_first_move": "Move selected", "list_multi_add_title_last": "songs to...", "list_name_default": "Audition list", "list_name_love": "My favorites", "list_name_temp": "Temporary list", "list_remove": "Remove", "list_remove_music_multi_tip": "Are you sure you want to remove the selected {num} songs?", "list_remove_tip": "Are you sure you want to remove \"{name}\"?", "list_remove_tip_button": "Yes, that's right", "list_rename": "<PERSON><PERSON>", "list_rename_title": "Rename list", "list_select_all": "Select all", "list_select_cancel": "Cancel", "list_select_local_file": "Add local songs", "list_select_local_file_desc": "Select local song folder", "list_select_local_file_empty_tip": "No songs found in the current folder", "list_select_local_file_result_failed_tip": "Found {total} songs, successfully added {success}, failed {failed}, you can check the error log for failed songs", "list_select_local_file_result_tip": "Found {total} songs, all added!", "list_select_local_file_temp_add_tip": "Found {total} files that meet the requirements, they have been quickly added to the current list, and now will enter the file tag reading process, please do not exit the application!", "list_select_range": "Range", "list_select_single": "Single", "list_select_unall": "Invert selection", "list_sort": "Sort songs", "list_sort_modal_by_album": "Album name", "list_sort_modal_by_down": "Descending", "list_sort_modal_by_field": "Sort field", "list_sort_modal_by_name": "Song name", "list_sort_modal_by_random": "Random", "list_sort_modal_by_singer": "Artist", "list_sort_modal_by_source": "Song source platform", "list_sort_modal_by_time": "Duration", "list_sort_modal_by_type": "Sort type", "list_sort_modal_by_up": "Ascending", "list_sync": "Update", "list_sync_confirm_tip": "This will replace the songs in \"{name}\" with the songs in the online list, are you sure you want to update?", "list_update_error": "\"{name}\" update failed", "list_update_success": "\"{name}\" updated successfully", "list_updating": "Updating", "lists__duplicate": "Duplicate songs", "lists_dislike_music_add_tip": "Added", "lists_dislike_music_singer_tip": "Do you really dislike \"{name}\" by \"{singer}\"?", "lists_dislike_music_tip": "Do you really dislike \"{name}\"?", "load_failed": "Ah~ Load failed😥", "loading": "Loading...", "location": "From {location}", "lyric__load_error": "Lyric acquisition failed", "metadata_edit_modal_confirm": "Save", "metadata_edit_modal_failed": "Save failed, please check the error log for details", "metadata_edit_modal_file_name": "File name", "metadata_edit_modal_file_path": "File path", "metadata_edit_modal_form_album_name": "Album name", "metadata_edit_modal_form_lyric": "LRC lyric", "metadata_edit_modal_form_match_lyric": "Online match", "metadata_edit_modal_form_match_lyric_failed": "Online lyric matching failed", "metadata_edit_modal_form_match_lyric_success": "Lyric matching successful🎉", "metadata_edit_modal_form_match_pic": "Online match", "metadata_edit_modal_form_match_pic_failed": "Online cover matching failed", "metadata_edit_modal_form_match_pic_success": "Cover matching successful🎉", "metadata_edit_modal_form_name": "Song name", "metadata_edit_modal_form_parse_name": "Parse song name and artist from file name", "metadata_edit_modal_form_parse_name_singer": "Song name - Artist", "metadata_edit_modal_form_parse_singer_name": "Artist - Song name", "metadata_edit_modal_form_pic": "Song album cover", "metadata_edit_modal_form_remove_lyric": "Clear", "metadata_edit_modal_form_remove_pic": "Remove image", "metadata_edit_modal_form_select_pic": "Select image", "metadata_edit_modal_form_select_pic_title": "Select song album cover image", "metadata_edit_modal_form_singer": "Artist", "metadata_edit_modal_processing": "Writing...", "metadata_edit_modal_success": "Saved successfully", "metadata_edit_modal_tip": "Song name cannot be empty", "metadata_edit_modal_title": "Edit song tags", "move_to": "Move to...", "music_source_detail": "Song details page", "name": "Song name: {name}", "nav_exit": "Exit app", "nav_love": "My list", "nav_search": "Search", "nav_setting": "Settings", "nav_songlist": "Songlist", "nav_top": "Toplist", "never_show": "Never show again", "no_item": "The list is empty...", "notifications_check_tip": "You have not allowed IKUN Music to display notifications, or the \"MusicService\" notification in the IKUN Music notification settings is disabled, which will prevent you from using the notification bar to pause, switch songs, etc. Do you want to enable it?", "notifications_check_title": "Notification permission reminder", "ok": "I know", "open_storage_error_tip": "The entered path is invalid", "open_storage_not_found_title": "External storage card not found, please manually enter the path below to specify the external storage", "open_storage_select_managed_folder_failed_tip": "Failed to select storage path: {msg}", "open_storage_select_path": "Open storage path", "open_storage_select_path_tip": "Tip: For external storage, if you still cannot access it after granting storage permission, you can click the button below to select the path to allow access.", "open_storage_tip": "Enter storage path", "open_storage_title": "Please manually enter the path below to specify the external storage", "parent_dir_name": "Parent folder", "pause": "Pause", "play": "Play", "play_all": "Play all", "play_detail_setting_lrc_align": "Lyric alignment", "play_detail_setting_lrc_align_center": "Center", "play_detail_setting_lrc_align_left": "Left", "play_detail_setting_lrc_align_right": "Right", "play_detail_setting_lrc_font_size": "Lyric font size", "play_detail_setting_playback_rate": "Playback rate", "play_detail_setting_playback_rate_reset": "Reset", "play_detail_setting_show_lyric_progress_setting": "Allow adjusting playback progress by dragging lyrics", "play_detail_setting_title": "Player settings", "play_detail_setting_volume": "Volume", "play_detail_todo_tip": "What do you want to do? No, this feature has not been implemented yet😛, but you can try to long press to locate the currently playing song (only valid for playing songs in \"My List\")", "play_later": "Play later", "play_list_loop": "List loop", "play_list_order": "Order play", "play_list_random": "Random play", "play_next": "Next", "play_prev": "Previous", "play_single": "Disable song switching", "play_single_loop": "Single loop", "player__buffering": "Buffering...", "player__end": "Playback finished", "player__error": "Audio loading error, switch to the next song in 5 seconds", "player__getting_url": "Getting song link...", "player__getting_url_delay_retry": "Server is busy, retry in {time} seconds...", "player__loading": "Loading music...", "player__refresh_url": "URL expired, refreshing URL...", "player_cache_migrating": "Migrating song cache, please wait ⌛️", "quality_high_quality": "HQ", "quality_lossless": "SQ", "quality_lossless_24bit": "24bit", "quality_lossless_atmos": "Atmos", "quality_lossless_atmos_plus": "Atmos2.0", "quality_lossless_master": "Master", "search__welcome": "Search for what I want~~😉", "search_history_search": "History search", "search_hot_search": "Hot search", "search_type_music": "Song", "search_type_songlist": "Songlist", "setting__other_dislike_list": "\"Disliked songs\" rules", "setting__other_dislike_list_label": "Number of rules: {num}", "setting__other_dislike_list_saved_tip": "Saved", "setting__other_lyric_raw_clear_btn": "Clear lyric cache", "setting__other_lyric_raw_label": "Number of lyrics:", "setting__other_meta_cache": "Other cache management", "setting__other_music_url_clear_btn": "Clear song URL cache", "setting__other_music_url_label": "Number of song URLs:", "setting__other_other_source_clear_btn": "Clear source-switched song cache", "setting__other_other_source_label": "Number of source-switched song information:", "setting__other_resource_cache": "Resource cache management (including song, image cache)", "setting_about": "About IKUN Music", "setting_backup": "Backup and restore", "setting_backup_all": "All data (list data and settings data)", "setting_backup_all_export": "Export", "setting_backup_all_export_desc": "Select backup save location", "setting_backup_all_import": "Import", "setting_backup_all_import_desc": "Select backup file", "setting_backup_part": "List data (universal with desktop version list backup file)", "setting_backup_part_export_list": "Export list", "setting_backup_part_export_list_desc": "Select songlist backup file save location", "setting_backup_part_export_list_tip_failed": "Songlist export failed", "setting_backup_part_export_list_tip_success": "Exported successfully", "setting_backup_part_export_list_tip_zip": "📦File packing...\nIt may take some time if the file is too large⏳", "setting_backup_part_export_setting": "Export settings", "setting_backup_part_export_setting_desc": "Select settings save location", "setting_backup_part_import_list": "Import list", "setting_backup_part_import_list_desc": "Select list backup file", "setting_backup_part_import_list_tip_error": "List import failed😕", "setting_backup_part_import_list_tip_running": "🚀Importing hard...\nIt may take some time if the list is too large⏳", "setting_backup_part_import_list_tip_success": "Imported successfully🎉", "setting_backup_part_import_list_tip_unzip": "📦File parsing...\nIt may take some time if the file is too large⏳", "setting_backup_part_import_setting": "Import settings", "setting_backup_part_import_setting_desc": "Select configuration file", "setting_backup_part_scan_local_music": "Scan local songs", "setting_backup_part_scan_local_music_start_tip": "Start scanning local songs", "setting_backup_part_scan_local_music_complete_tip": "<PERSON><PERSON> completed, found {num} songs", "setting_backup_part_scan_local_music_error_tip": "Failed to scan local songs: {message}", "setting_backup_part_scan_local_music_folder_desc": "Select local song folder", "setting_backup_part_scan_local_music_full": "Full disk scan (may scan non-music files)", "setting_backup_part_scan_local_music_folder_title": "Add local songs", "setting_backup_part_select_dir_title": "Select directory", "setting_basic": "Basic settings", "setting_basic_always_keep_statusbar_height": "Always keep status bar height", "setting_basic_always_keep_statusbar_height_tip": "By default, the software will dynamically determine whether to reserve space for the system status bar, but if the software's interactive content overlaps with the status bar content on your device, you can enable this option to always reserve space for the system status bar.", "setting_basic_animation": "Random animation for pop-up layer", "setting_basic_auto_hide_play_bar": "Automatically hide play bar when keyboard is popped up", "setting_basic_drawer_layout_position": "Navigation, favorite list pop-up direction", "setting_basic_drawer_layout_position_left": "Left", "setting_basic_drawer_layout_position_right": "Right", "setting_basic_font_size": "Font size setting (takes effect after restart)", "setting_basic_font_size_100": "Standard", "setting_basic_font_size_110": "Large", "setting_basic_font_size_120": "Larger", "setting_basic_font_size_130": "Very large", "setting_basic_font_size_80": "Smaller", "setting_basic_font_size_90": "Small", "setting_basic_font_size_preview": "IKUN Music font size preview", "setting_basic_home_page_scroll": "Enable horizontal scrolling of vertical home page", "setting_basic_lang": "Language", "setting_basic_share_type": "Share method", "setting_basic_share_type_clipboard": "Copy to clipboard", "setting_basic_share_type_system": "Use system share", "setting_basic_show_animation": "Show animation effect", "setting_basic_show_back_btn": "Show back to desktop button", "setting_basic_show_exit_btn": "Show exit app button", "setting_basic_source": "Custom source", "setting_basic_source_direct": "Audition interface (this is the last choice...)", "setting_basic_source_status_failed": "Initialization failed", "setting_basic_source_status_initing": "Initializing", "setting_basic_source_status_success": "Initialized successfully", "setting_basic_source_temp": "Temporary interface (some functions of the software are not available, it is recommended to use this interface only when the test interface is not available)", "setting_basic_source_test": "Test interface (almost all functions of the software are available)", "setting_basic_source_title": "Select custom source", "setting_basic_source_user_api_btn": "Custom source management", "setting_basic_sourcename": "Song source name", "setting_basic_sourcename_alias": "<PERSON><PERSON>", "setting_basic_sourcename_real": "Original name", "setting_basic_sourcename_title": "Select song source name type", "setting_basic_startup_auto_play": "Automatically play music after startup", "setting_basic_startup_push_play_detail_screen": "Open play details page after startup", "setting_basic_theme": "Theme color", "setting_basic_theme_auto_theme": "Follow the system's light and dark mode to switch themes", "setting_basic_theme_dynamic_bg": "Use dynamic background", "setting_basic_theme_font_shadow": "Enable font shadow", "setting_basic_theme_hide_bg_dark": "Hide black theme background", "setting_basic_theme_more_btn_show": "More themes", "setting_basic_use_system_file_selector": "Use system file selector", "setting_basic_use_system_file_selector_tip": "After enabling this option, importing backup files, custom sources, etc. will not require storage permission, but it may not be available on some systems.\n\nIf you cannot import files after enabling this option, you can disable this option and fall back to the software's built-in file selector.", "setting_dislike_list_input_tip": "Song name@Artist\nSong name\n@Artist", "setting_dislike_list_tips": "1. One per line, if the song or artist name contains the \"@\" symbol, you need to replace it with \"#\"\n2. Specify a song by a certain artist: Song name@Artist\n3. Specify a song: Song name\n4. Specify an artist: @Artist", "setting_list": "List settings", "setting_list_add_music_location_type": "Position when adding songs to the list", "setting_list_add_music_location_type_bottom": "Bottom", "setting_list_add_music_location_type_top": "Top", "setting_list_click_action": "Automatically switch to the current list to play when clicking on a song in the list (only valid for \"Songlist\" and \"Toplist\")", "setting_list_show interval": "Show song duration", "setting_list_show_album_name": "Show song album name", "setting_lyric_desktop": "Desktop lyric", "setting_lyric_desktop_enable": "Show lyric", "setting_lyric_desktop_lock": "Lock lyric", "setting_lyric_desktop_maxlineNum": "Maximum number of lines", "setting_lyric_desktop_permission_tip": "The desktop lyric function needs to grant IKUN Music the permission to display floating windows in the system permission settings to use it. Do you want to go to the relevant interface to grant this permission?", "setting_lyric_desktop_single_line": "Use single line lyric", "setting_lyric_desktop_text_opacity": "Lyric font opacity", "setting_lyric_desktop_text_size": "Lyric font size", "setting_lyric_desktop_text_x": "Lyric horizontal alignment", "setting_lyric_desktop_text_x_center": "Center", "setting_lyric_desktop_text_x_left": "Left", "setting_lyric_desktop_text_x_right": "Right", "setting_lyric_desktop_text_y": "Lyric vertical alignment", "setting_lyric_desktop_text_y_bottom": "Bottom", "setting_lyric_desktop_text_y_center": "Center", "setting_lyric_desktop_text_y_top": "Top", "setting_lyric_desktop_theme": "Lyric theme color", "setting_lyric_desktop_toggle_anima": "Show lyric switching animation", "setting_lyric_desktop_view_width": "Window percentage width", "setting_other": "Other", "setting_other_cache": "Cache management (including song, lyric, error log and other caches, it is not recommended to clear if there are no song playback related problems)", "setting_other_cache_clear_btn": "Clear cache", "setting_other_cache_clear_success_tip": "<PERSON><PERSON> cleared successfully", "setting_other_cache_getting": "Counting cache...", "setting_other_cache_size": "Current used cache size:", "setting_other_dislike_list_show_btn": "Edit rules", "setting_other_log": "Error log (log when an exception occurs during operation)", "setting_other_log_btn_clean": "Clear", "setting_other_log_btn_hide": "Close", "setting_other_log_btn_show": "View log", "setting_other_log_sync_log": "Record sync log", "setting_other_log_tip_clean_success": "Log cleared successfully", "setting_other_log_tip_null": "The log is empty~", "setting_other_log_user_api_log": "Record custom source log", "setting_other_local_music": "Local music", "setting_other_local_music_scan_button": "<PERSON><PERSON>", "setting_other_local_music_scaning": "Scanning...", "setting_other_local_music_scan_start_toast": "Start scanning local songs", "setting_other_local_music_scan_error_toast": "Failed to scan local songs: {error}", "setting_other_local_music_scan_end_toast": "<PERSON><PERSON> completed, found {num} songs", "setting_other_local_music_folder_path": "Folder path", "setting_other_local_music_folder_path_desc": "Scanned folder path, long press to delete", "setting_other_local_music_add_folder_button": "Add folder", "setting_other_local_music_add_folder_desc": "Select the folder to scan", "setting_other_local_music_add_folder_exist_tip": "This folder has been added", "setting_other_local_music_scan_dir_tip": "Scanning folder: {path}", "setting_other_local_music_is_music_tip": "Is it music", "setting_play_audio_offload": "Enable audio offload", "setting_play_audio_offload_tip": "Enabling audio offload can save power consumption, but on some devices, it may cause all songs to prompt \"audio loading error\" or \"cannot play the entire song\", which is caused by a bug in the current system.\n\nFor those who encounter this problem, you can disable this option and completely restart the application to try again.", "setting_play_auto_clean_played_list": "Automatically clear the played list", "setting_play_auto_clean_played_list_tip": "In random play mode, when switching songs by clicking on a song in the \"same list as the play list\", if \"Automatically clear the played list\" is enabled, the played songs will participate in random play again.", "setting_play_cache_size": "Maximum cache size (MB)", "setting_play_cache_size_no_cache": "Disable cache", "setting_play_cache_size_save_tip": "Cache setting is complete, it will take effect after restarting the application", "setting_play_handle_audio_focus": "Automatically pause playback when other apps play sound", "setting_play_handle_audio_focus_tip": "Takes effect after restarting the application", "setting_play_lyric_transition": "Show lyric translation", "setting_play_play_quality": "Priority playback quality (if available)", "setting_play_s2t": "Convert Chinese lyrics to traditional Chinese", "setting_play_save_play_time": "Remember playback progress", "setting_play_show_bluetooth_lyric": "Show bluetooth lyric", "setting_play_show_notification_image": "Show song album cover image in the notification bar", "setting_play_show_roma": "Show lyric romaji (if available)", "setting_play_show_translation": "Show lyric translation (if available)", "setting_player": "Player settings", "setting_player_save_play_time": "Remember playback progress", "setting_search": "Search settings", "setting_search_show_history_search": "Show history search records", "setting_search_show_hot_search": "Show hot search", "setting_sync": "Data sync", "setting_sync_address": "Current device address: {address}", "setting_sync_code_blocked_ip": "The IP of the current device has been blocked by the server!", "setting_sync_code_fail": "Invalid connection code", "setting_sync_code_input_tip": "Please enter the connection code", "setting_sync_code_label": "Enter the connection code for the first connection", "setting_sync_enable": "Enable sync", "setting_sync_history": "History address", "setting_sync_history_empty": "Nothing here😮", "setting_sync_history_title": "Connection history", "setting_sync_host_label": "Sync service address", "setting_sync_host_value_error_tip": "The address needs to start with \"http://\" or \"https://\"!", "setting_sync_host_value_tip": "http://<IP address>:<port number>", "setting_sync_port_label": "Sync service port number", "setting_sync_port_tip": "Please enter the sync service port number", "setting_sync_status": "Status: {status}", "setting_sync_status_enabled": "Connected", "setting_theme": "Theme settings", "setting_version": "Software update", "setting_version_show_ver_modal": "Open update window 🚀", "share_card_title_music": "Share \"{name}\" to...", "share_title_music": "Share song", "singer": "Artist: {name}", "songlist_hot": "Hot", "songlist_hot_collect": "Hot collect", "songlist_new": "New", "songlist_open": "Open", "songlist_open_input_placeholder": "Enter songlist link or songlist ID", "songlist_open_input_tip": "1. Cross-source opening of songlists is not supported, please confirm whether the songlist to be opened corresponds to the currently selected songlist source\n2. If you encounter a songlist link that cannot be opened, please feel free to give feedback\n3. Kugou source songlists do not support opening with songlist ID or concept version link, but support opening with normal version link or Kugou code", "songlist_recommend": "Recommend", "songlist_rise": "Rising", "songlist_tag_default": "<PERSON><PERSON><PERSON>", "songlist_tag_hot": "Hot", "songlist_tags": "Songlist category", "source_alias_all": "Aggregate conference", "source_alias_bd": "Xiaodu Music", "source_alias_kg": "Xiaogou Music", "source_alias_kw": "Xiaowo Music", "source_alias_mg": "Xiaomi Music", "source_alias_tx": "Xiaoqiu Music", "source_alias_wy": "Xiaoyun Music", "source_real_all": "Aggregate search", "source_real_bd": "Baidu Music", "source_real_kg": "Kugou Music", "source_real_kw": "Kuwo Music", "source_real_mg": "Migu Music", "source_real_tx": "Penguin Music", "source_real_wy": "Netease Music", "stop": "Stop", "stopped": "Stopped", "storage_file_no_match": "The selected file does not meet the requirements!", "storage_file_no_select_file_failed_tip": "Failed to select file using system file selector, do you want to fall back to the software's built-in file selector?", "storage_permission_tip_disagree": "You liar, I just asked you, you said you agreed, but you refused in the end, humph 🥺", "storage_permission_tip_disagree_ask_again": "This function cannot be used because you have permanently refused <PERSON><PERSON><PERSON> to access the phone storage😫.\nIf you want to continue, you need to go to 👉System Permission Management👈 to set <PERSON><PERSON><PERSON>'s storage permission to \"Allow\"", "storage_permission_tip_request": "To use this function, you need to allow <PERSON><PERSON><PERSON> to access the phone storage, do you agree and continue?", "sync__dislike_mode_merge_tip_desc": "Merge the content of the two lists and remove duplicates", "sync__dislike_mode_other_tip_desc": "\"Cancel sync\" will not use the \"disliked songs\" list sync function", "sync__dislike_mode_overwrite_tip_desc": "The list of the overwritten party will be replaced with the list of the overwriter", "sync__dislike_mode_title": "Select the sync method for the \"disliked songs\" list with \"{name}\"", "sync__list_mode_merge_tip_desc": "Merge the two lists together, the same songs will be removed (the songs of the merged party will be removed), and different songs will be added.", "sync__list_mode_other_tip_desc": "\"Cancel sync\" will not use the list sync function.", "sync__list_mode_overwrite_tip_desc": "The list with the same ID as the overwritten party will be deleted and replaced with the list of the overwriter (lists with different IDs will be merged together). If \"Completely overwrite\" is checked, all lists of the overwritten party will be removed and then replaced with the list of the overwriter.", "sync__list_mode_title": "Select the list sync method with \"{name}\"", "sync__mode_merge_btn_local_remote": "\"Local list\" merges \"remote list\"", "sync__mode_merge_btn_remote_local": "\"Remote list\" merges \"local list\"", "sync__mode_merge_tip": "Merge:", "sync__mode_other_label": "Other", "sync__mode_other_tip": "Other:", "sync__mode_overwrite": "Completely overwrite", "sync__mode_overwrite_btn_cancel": "Cancel sync", "sync__mode_overwrite_btn_local_remote": "\"Local list\" overwrites \"remote list\"", "sync__mode_overwrite_btn_remote_local": "\"Remote list\" overwrites \"local list\"", "sync__mode_overwrite_label": "Overwrite", "sync__mode_overwrite_tip": "Overwrite:", "sync_status_disabled": "Not connected", "theme_black": "Dark", "theme_blue": "Blue", "theme_blue2": "Light blue", "theme_blue_plus": "Dark blue", "theme_brown": "<PERSON>", "theme_china_ink": "Ink", "theme_green": "Green", "theme_grey": "Grey", "theme_happy_new_year": "Happy New Year", "theme_mid_autumn": "Mid-Autumn", "theme_ming": "<PERSON>", "theme_naruto": "<PERSON><PERSON><PERSON>", "theme_orange": "Orange", "theme_pink": "Pink", "theme_purple": "Purple", "theme_red": "Red", "timeout_exit_btn_cancel": "Cancel timer", "timeout_exit_btn_update": "Update timer", "timeout_exit_btn_wait_cancel": "Cancel exit", "timeout_exit_btn_wait_tip": "The timer has ended, waiting to exit...", "timeout_exit_input_tip": "Enter the countdown minutes", "timeout_exit_label_isPlayed": "Wait for the song to finish playing before stopping playback", "timeout_exit_min": "minutes", "timeout_exit_tip_cancel": "Timed stop playback cancelled", "timeout_exit_tip_max": "You can only set a maximum of {num} minutes", "timeout_exit_tip_off": "Set timed stop playback", "timeout_exit_tip_on": "Stop playback after {time}", "toggle_source": "Switch song source", "toggle_source_failed": "Failed to switch source, please try to manually specify other sources in the search page to search for this song to play", "toggle_source_try": "Trying to switch to other sources...", "understand": "Understood 👌", "user_api__init_failed_alert": "Custom source \"{name}\" initialization failed:", "user_api_add_failed_tip": "Invalid custom source file", "user_api_allow_show_update_alert": "Allow showing update pop-up", "user_api_btn_import": "Import", "user_api_btn_import_local": "Local import", "user_api_btn_import_online": "Online import", "user_api_btn_import_online_input_confirm": "Import", "user_api_btn_import_online_input_loading": "Importing...", "user_api_btn_import_online_input_tip": "Please enter HTTP link", "user_api_empty": "It's empty here 😲", "user_api_import_desc": "Select custom source file", "user_api_import_failed_tip": "Custom source import failed:\n{message}", "user_api_import_success_tip": "Imported successfully 🎉", "user_api_max_tip": "You can only have 20 sources at the same time🤪\nIf you want to continue importing, please remove some old sources to make room", "user_api_note": "Note: Although we have isolated the running environment of the script as much as possible, importing scripts containing malicious behavior may still affect your system, please import with caution.", "user_api_readme": "Source writing instructions:", "user_api_remove_tip": "Are you sure you want to remove \"{name}\"?", "user_api_title": "Custom source management (experimental)", "user_api_update_alert": "New version of custom source \"{name}\" found", "user_api_update_alert_open_url": "Open update address", "download_music_title": "Download {name} - {artist}", "version_btn_close": "Close", "version_btn_downloading": "Downloading hard... {current}/{total} ({progress}%)", "version_btn_failed": "Retry", "version_btn_ignore": "Ignore", "version_btn_ignore_cancel": "Cancel ignore", "version_btn_min": "Background download", "version_btn_new": "Update", "version_btn_unknown": "Project homepage", "version_btn_update": "Install", "version_label_change_log": "Update instructions:", "version_label_current_ver": "Current version:", "version_label_history": "History version:", "version_label_latest_ver": "Latest version:", "version_tip_checking": "Checking for updates...⏳", "version_tip_downloaded": "The installation package has been downloaded.", "version_tip_failed": "Failed to download the installation package, you can retry or go to the project address to manually download the new version to update.\n💡Tip: Generally, you can update normally by clicking retry a few more times!", "version_tip_latest": "The software is up to date, enjoy it~🥂", "version_tip_min": "Switched to background download, you can go to \"Settings → Software Update\" to reopen this pop-up window", "version_tip_unknown": "Failed to get the latest version information, it is recommended to manually go to the project address to check for new versions", "version_title_checking": "⏳ Checking for updates ⏳", "version_title_failed": "❌ Download failed ❌", "version_title_latest": "🎉 The current version is the latest 🎊", "version_title_new": "🌟 New version found 🌟", "version_title_unknown": "❓ Failed to get the latest version information ❓", "version_title_update": "🚀 Software update 🚀", "128k": "128K", "320k": "320K", "flac": "FLAC", "hires": "<PERSON><PERSON> Lossless 24-Bit", "atmos": "Atmos", "atmos_plus": "Atmos 2.0", "master": "Master", "setting_backup_part_scan_local_music_is_music": "Is it music"}