name: Build

on:
  # 暂时禁用自动触发，避免签名配置问题
  # push:
  #   branches:
  #     - main
  workflow_dispatch:

jobs:
  Android:
    name: Android
    runs-on: ubuntu-latest
    steps:
      - name: Check out git repository
        uses: actions/checkout@v4

      - name: Setup Env
        uses: ./.github/actions/setup

      - name: Build Packages
        shell: bash
        run: |
          cd android
          # 检查是否配置了签名密钥
          if [ -n "${{ secrets.KEYSTORE_STORE_FILE_BASE64 }}" ] && [ -n "${{ secrets.KEYSTORE_STORE_FILE }}" ]; then
            echo "Using configured keystore for release build..."
            echo ${{ secrets.KEYSTORE_STORE_FILE_BASE64 }} | base64 --decode > app/${{ secrets.KEYSTORE_STORE_FILE }}
            chmod +x ./gradlew
            ./gradlew assembleRelease -PMYAPP_UPLOAD_STORE_FILE='${{ secrets.KEYSTORE_STORE_FILE }}' -PMYAPP_UPLOAD_KEY_ALIAS='${{ secrets.KEYSTORE_KEY_ALIAS }}' -PMYAPP_UPLOAD_STORE_PASSWORD='${{ secrets.KEYSTORE_PASSWORD }}' -PMYAPP_UPLOAD_KEY_PASSWORD='${{ secrets.KEYSTORE_KEY_PASSWORD }}'
            rm -f app/${{ secrets.KEYSTORE_STORE_FILE }}
          else
            echo "No keystore configured, building debug APK instead..."
            chmod +x ./gradlew
            ./gradlew assembleDebug
            # 复制debug APK到release目录以保持工作流兼容性
            mkdir -p app/build/outputs/apk/release
            cp app/build/outputs/apk/debug/*.apk app/build/outputs/apk/release/ || echo "No debug APK found"
          fi

      # Push tag to GitHub if package.json version's tag is not tagged
      - name: Get package version
        run: node -p -e '`PACKAGE_VERSION=${require("./package.json").version}`' >> $GITHUB_ENV

      - name: Create git tag
        uses: pkgdeps/git-tag-action@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          github_repo: ${{ github.repository }}
          version: ${{ env.PACKAGE_VERSION }}
          git_commit_sha: ${{ github.sha }}
          git_tag_prefix: 'v'

      - name: Generate file MD5
        run: |
          cd android/app/build/outputs/apk/release
          md5sum *.apk

      - name: Upload Artifact
        uses: ./.github/actions/upload-artifact
        env:
          PACKAGE_VERSION: ${{ env.PACKAGE_VERSION }}

  # Android_SL:
  #   name: Android_SL
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Check out git repository
  #       uses: actions/checkout@v4
  #       with:
  #         ref: statusbar_lyric

  #     - name: Setup Env
  #       uses: ./.github/actions/setup

  #     - name: Build Packages
  #       shell: bash
  #       run: |
  #         cd android
  #         echo ${{ secrets.KEYSTORE_STORE_FILE_BASE64 }} | base64 --decode > app/${{ secrets.KEYSTORE_STORE_FILE }}
  #         ./gradlew assembleRelease -PMYAPP_UPLOAD_STORE_FILE='${{ secrets.KEYSTORE_STORE_FILE }}' -PMYAPP_UPLOAD_KEY_ALIAS='${{ secrets.KEYSTORE_KEY_ALIAS }}' -PMYAPP_UPLOAD_STORE_PASSWORD='${{ secrets.KEYSTORE_PASSWORD }}' -PMYAPP_UPLOAD_KEY_PASSWORD='${{ secrets.KEYSTORE_KEY_PASSWORD }}'
  #         rm -f app/${{ secrets.KEYSTORE_STORE_FILE }}

  #     # Push tag to GitHub if package.json version's tag is not tagged
  #     - name: Get package version
  #       run: |
  #         node -p -e '`PACKAGE_VERSION=${require("./package.json").version}`' >> $GITHUB_ENV
  #         echo "COMMIT_SHA=$(git show -s --format=%H)" >> $GITHUB_ENV

  #     - name: Create git tag
  #       uses: pkgdeps/git-tag-action@v3
  #       with:
  #         github_token: ${{ secrets.GITHUB_TOKEN }}
  #         github_repo: ${{ github.repository }}
  #         version: ${{ env.PACKAGE_VERSION }}
  #         git_commit_sha: ${{ env.COMMIT_SHA }}
  #         git_tag_prefix: "v"

  #     - name: Generate file MD5
  #       run: |
  #         echo "current commit sha: ${{ env.COMMIT_SHA }}"
  #         cd android/app/build/outputs/apk/release
  #         md5sum *.apk

  #     - name: Upload Artifact
  #       uses: ./.github/actions/upload-artifact
  #       env:
  #         PACKAGE_TYPE: 'Android_SL'
  #         PACKAGE_VERSION: ${{ env.PACKAGE_VERSION }}

  # Android_5:
  #   name: Android_5
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Check out git repository
  #       uses: actions/checkout@v4
  #       with:
  #         ref: android_5

  #     - name: Setup Env
  #       uses: ./.github/actions/setup

  #     - name: Build Packages
  #       shell: bash
  #       run: |
  #         cd android
  #         echo ${{ secrets.KEYSTORE_STORE_FILE_BASE64 }} | base64 --decode > app/${{ secrets.KEYSTORE_STORE_FILE }}
  #         ./gradlew assembleRelease -PMYAPP_UPLOAD_STORE_FILE='${{ secrets.KEYSTORE_STORE_FILE }}' -PMYAPP_UPLOAD_KEY_ALIAS='${{ secrets.KEYSTORE_KEY_ALIAS }}' -PMYAPP_UPLOAD_STORE_PASSWORD='${{ secrets.KEYSTORE_PASSWORD }}' -PMYAPP_UPLOAD_KEY_PASSWORD='${{ secrets.KEYSTORE_KEY_PASSWORD }}'
  #         rm -f app/${{ secrets.KEYSTORE_STORE_FILE }}

  #     # Push tag to GitHub if package.json version's tag is not tagged
  #     - name: Get package version
  #       run: |
  #         node -p -e '`PACKAGE_VERSION=${require("./package.json").version}`' >> $GITHUB_ENV
  #         echo "COMMIT_SHA=$(git show -s --format=%H)" >> $GITHUB_ENV

  #     - name: Create git tag
  #       uses: pkgdeps/git-tag-action@v3
  #       with:
  #         github_token: ${{ secrets.GITHUB_TOKEN }}
  #         github_repo: ${{ github.repository }}
  #         version: ${{ env.PACKAGE_VERSION }}
  #         git_commit_sha: ${{ env.COMMIT_SHA }}
  #         git_tag_prefix: "v"

  #     - name: Generate file MD5
  #       run: |
  #         echo "current commit sha: ${{ env.COMMIT_SHA }}"
  #         cd android/app/build/outputs/apk/release
  #         md5sum *.apk

  #     - name: Upload Artifact
  #       uses: ./.github/actions/upload-artifact
  #       env:
  #         PACKAGE_TYPE: 'Android_5'
  #         PACKAGE_VERSION: ${{ env.PACKAGE_VERSION }}

  Release:
    name: Release
    runs-on: ubuntu-latest
    # needs: [Android, Android_SL]
    needs: [Android]
    steps:
      - name: Check out git repository
        uses: actions/checkout@v4

      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./artifacts
          merge-multiple: true

      - name: Generate file MD5
        run: |
          echo -e '\n### File MD5\n```' >> ./publish/changeLog.md
          cd artifacts
          md5sum *.apk >> ../publish/changeLog.md
          echo -e '```\n' >> ../publish/changeLog.md
          echo -e '\n[软件安装包说明](https://lyswhut.github.io/lx-music-doc/download#%E8%BD%AF%E4%BB%B6%E5%AE%89%E8%A3%85%E5%8C%85%E8%AF%B4%E6%98%8E)\n' >> ../publish/changeLog.md

      - name: Get package version
        run: node -p -e '`PACKAGE_VERSION=${require("./package.json").version}`' >> $GITHUB_ENV

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          body_path: ./publish/changeLog.md
          prerelease: false
          draft: false
          tag_name: v${{ env.PACKAGE_VERSION }}
          files: |
            artifacts/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-arm64-v8a.apk
            artifacts/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-armeabi-v7a.apk
            artifacts/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-x86_64.apk
            artifacts/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-x86.apk
            artifacts/ikun-music-mobile-v${{ env.PACKAGE_VERSION }}-universal.apk

          # artifacts/lx-music-mobile-v${{ env.PACKAGE_VERSION }}-sl-arm64-v8a.apk
          # artifacts/lx-music-mobile-v${{ env.PACKAGE_VERSION }}-android_5-universal.apk
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
