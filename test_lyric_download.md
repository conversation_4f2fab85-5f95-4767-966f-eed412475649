# 歌词下载功能测试指南

## 功能概述
我们已经成功添加了下载歌曲时自动下载歌词的功能。该功能包括：

1. **默认设置**: 新增 `download.autoDownloadLyric` 设置项，默认为 `true`
2. **歌词文件保存**: 自动将歌词保存为 `.lrc` 文件到音频文件相同目录
3. **用户界面**: 在下载模态框中添加"同时下载歌词文件"选项
4. **智能处理**: 如果获取歌词失败，不会影响音频文件下载

## 测试步骤

### 1. 基本功能测试
1. 打开应用，进入任意音乐列表（我的列表或在线搜索）
2. 长按任意歌曲，选择"下载"选项
3. 在下载模态框中，确认"同时下载歌词文件"选项已勾选
4. 选择音质，点击确认下载
5. 检查下载目录 `/storage/emulated/0/Music/IKUN Music/` 是否同时包含音频文件和 `.lrc` 歌词文件

### 2. 设置项测试
1. 进入应用设置
2. 查找"下载时自动获取歌词"选项（需要在设置界面中添加）
3. 切换该选项的开关状态
4. 重新测试下载功能，确认设置生效

### 3. 错误处理测试
1. 选择一首可能没有歌词的歌曲进行下载
2. 确认音频文件正常下载，即使歌词获取失败
3. 检查控制台日志，确认错误处理正确

### 4. 用户体验测试
1. 测试下载模态框中歌词选项的交互
2. 确认下载成功提示信息包含歌词下载状态
3. 验证歌词文件命名与音频文件一致

## 文件结构
下载后的文件结构应该如下：
```
/storage/emulated/0/Music/IKUN Music/
├── 歌曲名 - 歌手名 - 128k.mp3
├── 歌曲名 - 歌手名.lrc
├── 另一首歌 - 歌手名 - 320k.flac
└── 另一首歌 - 歌手名.lrc
```

## 已实现的功能
- ✅ 添加下载设置选项 (`download.autoDownloadLyric`)
- ✅ 实现歌词文件保存功能
- ✅ 修改下载逻辑以支持歌词下载
- ✅ 更新下载界面，添加歌词下载选项
- ✅ 错误处理和用户体验优化

## 注意事项
1. 歌词文件保存失败不会影响音频文件下载
2. 歌词文件使用 UTF-8 编码保存
3. 如果歌词为空或获取失败，会在控制台输出相应日志
4. 下载目录会自动创建（如果不存在）
