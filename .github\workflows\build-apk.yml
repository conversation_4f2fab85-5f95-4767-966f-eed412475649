name: Build Android APK

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Setup Java JDK
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'

    - name: Setup Android SDK
      uses: android-actions/setup-android@v3

    - name: Install dependencies
      run: |
        echo "Installing npm dependencies..."
        npm ci --prefer-offline --no-audit
        echo "Verifying React Native installation..."
        ls -la node_modules/react-native/ || echo "React Native not found"
        ls -la node_modules/@react-native-community/cli-platform-android/ || echo "CLI platform android not found"
        echo "Checking critical files..."
        ls -la node_modules/@react-native-community/cli-platform-android/native_modules.gradle || echo "native_modules.gradle not found"
        echo "React Native version:"
        cat node_modules/react-native/package.json | grep '"version"' || echo "Version not found"

    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Make gradlew executable
      run: chmod +x android/gradlew

    - name: Create assets directory
      run: mkdir -p android/app/src/main/assets

    - name: Build Debug APK
      run: |
        cd android
        ./gradlew clean
        ./gradlew assembleDebug

    - name: Generate JS Bundle for Release
      run: |
        echo "Generating JavaScript bundle for Release..."
        mkdir -p android/app/src/main/assets
        npx react-native bundle \
          --platform android \
          --dev false \
          --entry-file index.js \
          --bundle-output android/app/src/main/assets/index.android.bundle \
          --assets-dest android/app/src/main/res/ \
          --reset-cache

        echo "Bundle generated successfully"
        ls -la android/app/src/main/assets/ || echo "Assets directory not found"

    - name: Build Release APK
      run: |
        cd android
        echo "Building release APK..."
        ./gradlew assembleRelease
      continue-on-error: true

    - name: List APK files
      run: |
        echo "=== Debug APK files ==="
        find android/app/build/outputs/apk/debug -name "*.apk" || echo "No debug APK files found"
        echo "=== Release APK files ==="
        find android/app/build/outputs/apk/release -name "*.apk" || echo "No release APK files found"

    - name: Upload Debug APK
      uses: actions/upload-artifact@v4
      with:
        name: debug-apk
        path: android/app/build/outputs/apk/debug/*.apk
        if-no-files-found: error

    - name: Upload Release APK (if exists)
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: release-apk
        path: android/app/build/outputs/apk/release/*.apk
        if-no-files-found: warn

    - name: Create Release (manual)
      if: github.ref == 'refs/heads/main'
      run: |
        echo "🎉 构建完成！"
        echo "📱 APK文件已上传到Artifacts，请从Actions页面下载："
        echo "   - Debug APK: 适用于测试，包含调试信息"
        echo "   - Release APK: 优化版本（如果构建成功）"
        echo ""
        echo "📋 下载步骤："
        echo "1. 访问: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        echo "2. 滚动到页面底部的 'Artifacts' 部分"
        echo "3. 点击下载对应的APK文件"
        echo "4. 解压下载的zip文件获取APK"
