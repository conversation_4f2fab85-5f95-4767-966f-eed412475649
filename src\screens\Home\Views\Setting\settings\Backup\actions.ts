import { LIST_IDS } from '@/config/constant'
import {
  createList,
  getListMusics,
  overwriteList,
  overwriteListFull,
  overwriteListMusics,
} from '@/core/list'
import { filterMusicList, fixNewMusicInfoQuality, toNewMusicInfo } from '@/utils'
import { log } from '@/utils/log'
import { confirmDialog, getFormattedDate, handleReadFile, handleSaveFile, showImportTip, toast } from '@/utils/tools'
import listState from '@/store/list/state'

const getAllLists = async () => {
  const lists = []
  lists.push(
    await getListMusics(listState.defaultList.id).then((musics) => ({
      ...listState.defaultList,
      list: musics,
    }))
  )
  lists.push(
    await getListMusics(listState.loveList.id).then((musics) => ({
      ...listState.loveList,
      list: musics,
    }))
  )

  for await (const list of listState.userList) {
    lists.push(await getListMusics(list.id).then((musics) => ({ ...list, list: musics })))
  }

  return lists
}
const importOldListData = async (lists: any[]) => {
  const allLists = await getAllLists()
  for (const list of lists) {
    try {
      const targetList = allLists.find((l) => l.id == list.id)
      if (targetList) {
        targetList.list = filterMusicList((list.list as any[]).map((m) => toNewMusicInfo(m)))
      } else {
        const listInfo = {
          name: list.name,
          id: list.id,
          list: filterMusicList((list.list as any[]).map((m) => toNewMusicInfo(m))),
          source: list.source,
          sourceListId: list.sourceListId,
          locationUpdateTime: list.locationUpdateTime ?? null,
        }
        allLists.push(listInfo as LX.List.UserListInfoFull)
      }
    } catch (err) {
      console.log(err)
    }
  }
  const defaultList = allLists.shift()!.list
  const loveList = allLists.shift()!.list
  await overwriteListFull({
    defaultList,
    loveList,
    userList: allLists as LX.List.UserListInfoFull[],
  })
}
const importNewListData = async (
  lists: Array<
    LX.List.MyDefaultListInfoFull | LX.List.MyLoveListInfoFull | LX.List.UserListInfoFull
  >
) => {
  const allLists = await getAllLists()
  for (const list of lists) {
    try {
      const targetList = allLists.find((l) => l.id == list.id)
      if (targetList) {
        targetList.list = filterMusicList(list.list).map((m) => fixNewMusicInfoQuality(m))
      } else {
        const data = {
          name: list.name,
          id: list.id,
          list: filterMusicList(list.list).map((m) => fixNewMusicInfoQuality(m)),
          source: (list as LX.List.UserListInfoFull).source,
          sourceListId: (list as LX.List.UserListInfoFull).sourceListId,
          locationUpdateTime: (list as LX.List.UserListInfoFull).locationUpdateTime ?? null,
        }
        allLists.push(data as LX.List.UserListInfoFull)
      }
    } catch (err) {
      console.log(err)
    }
  }
  const defaultList = allLists.shift()!.list
  const loveList = allLists.shift()!.list
  await overwriteListFull({
    defaultList,
    loveList,
    userList: allLists as LX.List.UserListInfoFull[],
  })
}

/**
 * 导入单个列表
 * @param listData
 * @param position
 * @returns
 */
export const handleImportListPart = async (
  listData: LX.ConfigFile.MyListInfoPart['data'],
  position: number = listState.userList.length
) => {
  const targetList = listState.allList.find((l) => l.id === listData.id)
  if (targetList) {
    const confirm = await confirmDialog({
      message: global.i18n.t('list_import_part_confirm', {
        importName: listData.name,
        localName: targetList.name,
      }),
      cancelButtonText: global.i18n.t('list_import_part_button_cancel'),
      confirmButtonText: global.i18n.t('list_import_part_button_confirm'),
      bgClose: false,
    })
    if (confirm) {
      listData.name = targetList.name
      void overwriteList(listData)
        .then(() => {
          toast(global.i18n.t('setting_backup_part_import_list_tip_success'))
        })
        .catch((err) => {
          log.error(err)
          toast(global.i18n.t('setting_backup_part_import_list_tip_error'))
        })
      return
    }
    const timeStr = getFormattedDate()
    listData.id += `__${timeStr}`
    listData.name += `__${timeStr}`
  }
  const userList = listData as LX.List.UserListInfoFull
  void createList({
    name: userList.name,
    id: userList.id,
    list: userList.list,
    source: userList.source,
    sourceListId: userList.sourceListId,
    position: Math.max(position, -1),
  })
    .then(() => {
      toast(global.i18n.t('setting_backup_part_import_list_tip_success'))
    })
    .catch((err) => {
      log.error(err)
      toast(global.i18n.t('setting_backup_part_import_list_tip_error'))
    })
}

const importPlayList = async (path: string) => {
  let configData: any
  try {
    configData = await handleReadFile(path)
  } catch (error: any) {
    log.error(error.stack)
    throw error
  }

  switch (configData.type) {
    case 'defautlList': // 兼容0.6.2及以前版本的列表数据
      await overwriteListMusics(
        LIST_IDS.DEFAULT,
        filterMusicList(
          (configData.data as LX.List.MyDefaultListInfoFull).list.map((m) => toNewMusicInfo(m))
        )
      )
      break
    case 'playList':
      await importOldListData(configData.data)
      break
    case 'playList_v2':
      await importNewListData(configData.data)
      break
    case 'allData':
      // 兼容0.6.2及以前版本的列表数据
      if (configData.defaultList)
        await overwriteListMusics(
          LIST_IDS.DEFAULT,
          filterMusicList(
            (configData.defaultList as LX.List.MyDefaultListInfoFull).list.map((m) =>
              toNewMusicInfo(m)
            )
          )
        )
      else await importOldListData(configData.playList)
      break
    case 'allData_v2':
      await importNewListData(configData.playList)
      break
    case 'playListPart':
      configData.data.list = filterMusicList(
        (configData.data as LX.ConfigFile.MyListInfoPart['data']).list.map((m) => toNewMusicInfo(m))
      )

      void handleImportListPart(configData.data)
      return true
    case 'playListPart_v2':
      configData.data.list = filterMusicList(
        (configData.data as LX.ConfigFile.MyListInfoPart['data']).list
      ).map((m) => fixNewMusicInfoQuality(m))

      void handleImportListPart(configData.data)
      return true

    default:
      showImportTip(configData.type)
  }
}

export const handleImportList = (path: string) => {
  console.log(path)
  toast(global.i18n.t('setting_backup_part_import_list_tip_unzip'))
  void importPlayList(path)
    .then((skipTip) => {
      if (skipTip) return
      toast(global.i18n.t('setting_backup_part_import_list_tip_success'))
    })
    .catch((err) => {
      log.error(err)
      toast(global.i18n.t('setting_backup_part_import_list_tip_error'))
    })
}

const exportAllList = async (path: string) => {
  const data = JSON.parse(
    JSON.stringify({
      type: 'playList_v2',
      data: await getAllLists(),
    })
  )

  try {
    await handleSaveFile(path + '/lx_list.lxmc', data)
  } catch (error: any) {
    log.error(error.stack)
  }
}
export const handleExportList = (path: string) => {
  toast(global.i18n.t('setting_backup_part_export_list_tip_zip'))
  void exportAllList(path)
    .then(() => {
      toast(global.i18n.t('setting_backup_part_export_list_tip_success'))
    })
    .catch((err: any) => {
      log.error(err.message)
      toast(
        global.i18n.t('setting_backup_part_export_list_tip_failed') + ': ' + (err.message as string)
      )
    })
}
