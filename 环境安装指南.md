# 🛠️ Android开发环境安装指南

## 📋 安装清单

根据检测，您需要安装以下组件：

- [ ] Java Development Kit (JDK) 17
- [ ] Android Studio
- [ ] Android SDK
- [ ] 环境变量配置

## 🚀 快速安装步骤

### 1. 安装 Java JDK 17

#### 下载安装
1. 访问 [Adoptium](https://adoptium.net/)
2. 选择 **OpenJDK 17 (LTS)**
3. 选择您的操作系统 (Windows x64)
4. 下载并安装

#### 验证安装
```cmd
java -version
javac -version
```

#### 设置环境变量
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 新建系统变量：
   - 变量名: `JAVA_HOME`
   - 变量值: `C:\Program Files\Eclipse Adoptium\jdk-17.x.x-hotspot`
4. 编辑 `Path` 变量，添加: `%JAVA_HOME%\bin`

### 2. 安装 Android Studio

#### 下载安装
1. 访问 [Android Studio官网](https://developer.android.com/studio)
2. 下载最新版本
3. 运行安装程序，选择标准安装

#### 首次启动配置
1. 启动Android Studio
2. 选择 "Standard" 安装类型
3. 等待SDK组件下载完成
4. 创建虚拟设备 (可选)

### 3. 配置环境变量

#### 设置 ANDROID_HOME
1. 找到Android SDK安装路径 (通常在 `C:\Users\<USER>\AppData\Local\Android\Sdk`)
2. 新建系统变量：
   - 变量名: `ANDROID_HOME`
   - 变量值: SDK路径
3. 编辑 `Path` 变量，添加：
   - `%ANDROID_HOME%\platform-tools`
   - `%ANDROID_HOME%\tools`
   - `%ANDROID_HOME%\tools\bin`

#### 验证配置
```cmd
adb version
```

### 4. 安装必需的SDK组件

在Android Studio中：
1. 打开 "SDK Manager" (Tools → SDK Manager)
2. 确保安装以下组件：
   - Android SDK Platform 35
   - Android SDK Build-Tools 35.0.0
   - Android SDK Platform-Tools
   - Android SDK Tools

## 🔧 环境验证

运行以下命令验证环境：

```cmd
# 检查React Native环境
npx react-native doctor

# 检查各组件版本
java -version
adb version
node --version
npm --version
```

## 🎯 构建APK

环境配置完成后，运行构建脚本：

```cmd
# 使用自动化脚本
build-apk.bat

# 或手动执行
npm install
cd android
gradlew.bat clean
gradlew.bat assembleDebug
```

## 🚨 常见问题解决

### 问题1: JAVA_HOME未设置
```
ERROR: JAVA_HOME is not set
```
**解决**: 按照上述步骤设置JAVA_HOME环境变量

### 问题2: Android SDK未找到
```
SDK location not found
```
**解决**: 设置ANDROID_HOME环境变量指向SDK目录

### 问题3: 构建内存不足
```
OutOfMemoryError
```
**解决**: 在 `android/gradle.properties` 中增加：
```properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=2048m
```

### 问题4: 网络连接问题
**解决**: 配置代理或使用国内镜像：
```properties
# 在 android/gradle.properties 中添加
systemProp.http.proxyHost=127.0.0.1
systemProp.http.proxyPort=7890
systemProp.https.proxyHost=127.0.0.1
systemProp.https.proxyPort=7890
```

## 📱 替代方案

如果本地环境配置困难，可以考虑：

### 1. GitHub Actions自动构建
1. Fork项目到您的GitHub
2. 启用Actions
3. 推送代码自动构建APK

### 2. 在线构建服务
- **EAS Build** (Expo)
- **Bitrise**
- **CircleCI**

### 3. 使用预构建APK
如果只是测试功能，可以使用项目Release页面的预构建APK。

## ✅ 完成检查

安装完成后，确认以下命令都能正常运行：

```cmd
java -version          # 显示JDK版本
adb version           # 显示ADB版本
npx react-native doctor  # 显示所有组件正常
```

全部显示正常后，就可以开始构建APK了！🎉
